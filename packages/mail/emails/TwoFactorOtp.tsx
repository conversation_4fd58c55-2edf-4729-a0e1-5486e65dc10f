import { Text } from "@react-email/components";
import React from "react";
import { createTranslator } from "use-intl/core";
import Wrapper from "../src/components/Wrapper";
import { defaultLocale } from "../src/util/translations";
import { defaultTranslations } from "../src/util/translations";
import type { BaseMailProps } from "../types";

export function TwoFactorOtp({
	otp,
	name,
	locale,
	translations,
}: {
	otp: string;
	name: string;
} & BaseMailProps) {
	const t = createTranslator({
		locale,
		messages: translations,
	});

	return (
		<Wrapper>
			<Text>{t("mail.twoFactorOtp.body", { name })}</Text>

			<Text className="text-center">
				{t("mail.common.otp")}
				<br />
				<strong className="font-bold text-3xl tracking-wider bg-gray-100 px-4 py-2 rounded-lg inline-block mt-2">
					{otp}
				</strong>
			</Text>

			<Text className="text-sm text-muted-foreground">
				{t("mail.twoFactorOtp.expiry")}
			</Text>

			<Text className="text-sm text-muted-foreground">
				{t("mail.twoFactorOtp.security")}
			</Text>
		</Wrapper>
	);
}

TwoFactorOtp.PreviewProps = {
	locale: defaultLocale,
	translations: defaultTranslations,
	otp: "123456",
	name: "John Doe",
};

export default TwoFactorOtp;
