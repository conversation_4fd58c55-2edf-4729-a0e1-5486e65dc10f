"use client";
import { authClient } from "@repo/auth/client";
import { useSession } from "@saas/auth/hooks/use-session";
import { useUserAccountsQuery } from "@saas/auth/lib/api";
import { SettingsItem } from "@saas/shared/components/SettingsItem";
import { useMutation } from "@tanstack/react-query";
import { Button } from "@ui/components/button";
import { Card } from "@ui/components/card";
import {
	<PERSON><PERSON>,
	DialogContent,
	DialogHeader,
	DialogTitle,
} from "@ui/components/dialog";
import { FormItem } from "@ui/components/form";
import { Input } from "@ui/components/input";
import { Label } from "@ui/components/label";
import { PasswordInput } from "@ui/components/password-input";
import {
	ArrowRightIcon,
	CheckIcon,
	CopyIcon,
	DownloadIcon,
	KeyIcon,
	RefreshCwIcon,
	ShieldCheckIcon,
	TabletSmartphoneIcon,
	XIcon,
} from "lucide-react";
import { useTranslations } from "next-intl";
import { useEffect, useMemo, useState } from "react";
import QRCode from "react-qr-code";
import { toast } from "sonner";

export function TwoFactorBlock() {
	const t = useTranslations();
	const { user, reloadSession } = useSession();

	const [dialogOpen, setDialogOpen] = useState(false);
	const [dialogView, setDialogView] = useState<
		"password" | "totp-url" | "backup-codes"
	>("password");
	const [totpURI, setTotpURI] = useState("");
	const [password, setPassword] = useState("");
	const [totpCode, setTotpCode] = useState("");
	const [backupCodes, setBackupCodes] = useState<string[]>([]);
	const [showBackupCodes, setShowBackupCodes] = useState(false);

	const { data: accounts } = useUserAccountsQuery();

	useEffect(() => {
		setPassword("");
	}, [dialogOpen]);

	const totpURISecret = useMemo(() => {
		if (!totpURI) {
			return null;
		}

		const url = new URL(totpURI);
		return url.searchParams.get("secret") || null;
	}, [totpURI]);

	const verifyPassword = async () => {
		setDialogView("password");
		setDialogOpen(true);
	};

	const copyBackupCodes = async () => {
		const codesText = backupCodes.join("\n");
		await navigator.clipboard.writeText(codesText);
		toast.success(t("settings.account.security.twoFactor.backupCodes.copied"));
	};

	const downloadBackupCodes = () => {
		const codesText = backupCodes.join("\n");
		const blob = new Blob([codesText], { type: "text/plain" });
		const url = URL.createObjectURL(blob);
		const a = document.createElement("a");
		a.href = url;
		a.download = "backup-codes.txt";
		document.body.appendChild(a);
		a.click();
		document.body.removeChild(a);
		URL.revokeObjectURL(url);
		toast.success(
			t("settings.account.security.twoFactor.backupCodes.downloaded"),
		);
	};

	const regenerateBackupCodes = () => {
		setDialogView("password");
		setShowBackupCodes(true);
	};

	const enableTwoFactorMutation = useMutation({
		mutationKey: ["enableTwoFactor"],
		mutationFn: async () => {
			const { data, error } = await authClient.twoFactor.enable({
				password,
			});

			if (error) {
				throw error;
			}

			setTotpURI(data.totpURI);
			setBackupCodes(data.backupCodes || []);
			setDialogView("totp-url");
		},

		onError: () => {
			toast.error(
				t(
					"settings.account.security.twoFactor.notifications.enable.error.title",
				),
			);
		},
	});

	const disableTwoFactorMutation = useMutation({
		mutationKey: ["disableTwoFactor"],
		mutationFn: async () => {
			const { data, error } = await authClient.twoFactor.disable({
				password,
			});

			if (error) {
				throw error;
			}

			setDialogOpen(false);

			toast.success(
				t(
					"settings.account.security.twoFactor.notifications.disable.success.title",
				),
			);

			reloadSession();
		},

		onError: () => {
			toast.error(
				t(
					"settings.account.security.twoFactor.notifications.enable.error.title",
				),
			);
		},
	});

	const verifyTwoFactorMutation = useMutation({
		mutationKey: ["verifyTwoFactor"],
		mutationFn: async () => {
			const { error } = await authClient.twoFactor.verifyTotp({
				code: totpCode,
			});

			if (error) {
				throw error;
			}

			toast.success(
				t(
					"settings.account.security.twoFactor.notifications.verify.success.title",
				),
			);

			reloadSession();
			setDialogView("backup-codes");
		},
	});

	const generateBackupCodesMutation = useMutation({
		mutationKey: ["generateBackupCodes"],
		mutationFn: async () => {
			const { data, error } = await authClient.twoFactor.generateBackupCodes({
				password,
			});

			if (error) {
				throw error;
			}

			setBackupCodes(data.backupCodes || []);
			setDialogView("backup-codes");
		},
		onError: () => {
			toast.error(
				t(
					"settings.account.security.twoFactor.notifications.enable.error.title",
				),
			);
		},
	});

	const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
		e.preventDefault();

		if (user?.twoFactorEnabled) {
			disableTwoFactorMutation.mutate();
			return;
		}

		if (dialogView === "password") {
			if (showBackupCodes) {
				generateBackupCodesMutation.mutate();
			} else {
				enableTwoFactorMutation.mutate();
			}
			return;
		}

		if (dialogView === "backup-codes") {
			setDialogOpen(false);
			return;
		}

		verifyTwoFactorMutation.mutate();
	};

	if (!accounts?.some((account) => account.provider === "credential")) {
		return null;
	}

	return (
		<SettingsItem
			title={t("settings.account.security.twoFactor.title")}
			description={t("settings.account.security.twoFactor.description")}
		>
			{user?.twoFactorEnabled ? (
				<div className="flex items-start flex-col gap-4">
					<div className="flex items-center gap-1.5">
						<ShieldCheckIcon className="size-6 text-green-500" />
						<p className="text-sm text-foreground">
							{t("settings.account.security.twoFactor.enabled")}
						</p>
					</div>
					<div className="flex gap-2 flex-wrap">
						<Button variant="light" onClick={regenerateBackupCodes}>
							<KeyIcon className="mr-1.5 size-4" />
							{t("settings.account.security.twoFactor.backupCodes.viewCodes")}
						</Button>
						<Button variant="light" onClick={verifyPassword}>
							<XIcon className="mr-1.5 size-4" />
							{t("settings.account.security.twoFactor.disable")}
						</Button>
					</div>
				</div>
			) : (
				<div className="flex justify-start">
					<Button variant="light" onClick={verifyPassword}>
						<TabletSmartphoneIcon className="mr-1.5 size-4" />
						{t("settings.account.security.twoFactor.enable")}
					</Button>
				</div>
			)}

			<Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
				<DialogContent>
					<DialogHeader>
						<DialogTitle>
							{dialogView === "password"
								? t(
										"settings.account.security.twoFactor.dialog.password.title",
									)
								: dialogView === "backup-codes"
									? t("settings.account.security.twoFactor.backupCodes.title")
									: t(
											"settings.account.security.twoFactor.dialog.totpUrl.title",
										)}
						</DialogTitle>
					</DialogHeader>

					{dialogView === "password" ? (
						<form onSubmit={handleSubmit}>
							<div className="grid grid-cols-1 gap-4">
								<p className="text-sm text-foreground/60">
									{t(
										"settings.account.security.twoFactor.dialog.password.description",
									)}
								</p>

								<FormItem>
									<Label className="block">
										{t(
											"settings.account.security.twoFactor.dialog.password.label",
										)}
									</Label>
									<PasswordInput
										value={password}
										onChange={(value) => setPassword(value)}
									/>
								</FormItem>
							</div>
							<div className="mt-4">
								<Button
									type="submit"
									variant="secondary"
									className="w-full"
									loading={
										enableTwoFactorMutation.isPending ||
										disableTwoFactorMutation.isPending
									}
								>
									{t("common.actions.continue")}
									<ArrowRightIcon className="ml-1.5 size-4" />
								</Button>
							</div>
						</form>
					) : (
						<form onSubmit={handleSubmit}>
							<div className="grid grid-cols-1 gap-4">
								<p className="text-sm text-foreground/60">
									{t(
										"settings.account.security.twoFactor.dialog.totpUrl.description",
									)}
								</p>
								<Card className="flex flex-col items-center gap-4 p-6">
									<QRCode value={totpURI} />

									{totpURISecret && (
										<p className="text-xs text-muted-foreground text-center">
											{totpURISecret}
										</p>
									)}
								</Card>

								<hr />

								<div className="grid grid-cols-1 gap-4">
									<FormItem>
										<Label className="block">
											{t(
												"settings.account.security.twoFactor.dialog.totpUrl.code",
											)}
										</Label>
										<Input
											value={totpCode}
											onChange={(e) =>
												setTotpCode(e.target.value)
											}
										/>
									</FormItem>
								</div>
							</div>
							<div className="mt-4">
								<Button
									type="submit"
									variant="secondary"
									className="w-full"
									loading={verifyTwoFactorMutation.isPending}
								>
									<CheckIcon className="mr-1.5 size-4" />
									{t("common.actions.verify")}
								</Button>
							</div>
						</form>
					) : dialogView === "backup-codes" ? (
						<div className="grid grid-cols-1 gap-4">
							<p className="text-sm text-foreground/60">
								{t("settings.account.security.twoFactor.backupCodes.description")}
							</p>

							<Card className="p-4">
								<div className="grid grid-cols-2 gap-2 font-mono text-sm">
									{backupCodes.map((code, index) => (
										<div
											key={index}
											className="p-2 bg-muted rounded text-center"
										>
											{code}
										</div>
									))}
								</div>
							</Card>

							<p className="text-sm text-amber-600 font-medium">
								{t("settings.account.security.twoFactor.backupCodes.warning")}
							</p>

							<div className="flex gap-2">
								<Button
									variant="outline"
									onClick={copyBackupCodes}
									className="flex-1"
								>
									<CopyIcon className="mr-1.5 size-4" />
									{t("settings.account.security.twoFactor.backupCodes.copy")}
								</Button>
								<Button
									variant="outline"
									onClick={downloadBackupCodes}
									className="flex-1"
								>
									<DownloadIcon className="mr-1.5 size-4" />
									{t("settings.account.security.twoFactor.backupCodes.download")}
								</Button>
							</div>

							<Button
								onClick={() => setDialogOpen(false)}
								className="w-full"
							>
								{t("common.actions.done")}
							</Button>
						</div>
					) : null}
				</DialogContent>
			</Dialog>
		</SettingsItem>
	);
}
