"use client";

import { zod<PERSON><PERSON>olver } from "@hookform/resolvers/zod";
import { <PERSON><PERSON>, AlertTitle } from "@ui/components/alert";
import { Button } from "@ui/components/button";
import { Checkbox } from "@ui/components/checkbox";
import { AlertTriangleIcon, ArrowLeftIcon, MailIcon, RefreshCwIcon } from "lucide-react";

import { authClient } from "@repo/auth/client";
import { config } from "@repo/config";
import { useAuthErrorMessages } from "@saas/auth/hooks/errors-messages";
import { useRouter } from "@shared/hooks/router";
import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from "@ui/components/form";
import {
	InputOTP,
	InputOTPGroup,
	InputOTPSeparator,
	InputOTPSlot,
} from "@ui/components/input-otp";
import { useTranslations } from "next-intl";
import Link from "next/link";
import { useSearchParams } from "next/navigation";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import * as z from "zod";

const formSchema = z.object({
	code: z.string().min(6).max(6),
	trustDevice: z.boolean().default(false),
});

type FormValues = z.infer<typeof formSchema>;

export function OtpForm() {
	const t = useTranslations();
	const router = useRouter();
	const { getAuthErrorMessage } = useAuthErrorMessages();
	const searchParams = useSearchParams();

	const [verificationMethod, setVerificationMethod] = useState<"totp" | "otp" | "backup">("totp");
	const [isLoadingOtp, setIsLoadingOtp] = useState(false);

	const invitationId = searchParams.get("invitationId");
	const redirectTo = searchParams.get("redirectTo");

	const redirectPath = invitationId
		? `/app/organization-invitation/${invitationId}`
		: (redirectTo ?? config.auth.redirectAfterSignIn);

	const form = useForm<FormValues>({
		resolver: zodResolver(formSchema),
		defaultValues: {
			code: "",
			trustDevice: false,
		},
	});

	const sendOtp = async () => {
		setIsLoadingOtp(true);
		try {
			const { error } = await authClient.twoFactor.sendOtp({
				trustDevice: form.getValues("trustDevice"),
			});

			if (error) {
				throw error;
			}

			toast.success(t("auth.verify.otpSent"));
			setVerificationMethod("otp");
		} catch (e) {
			form.setError("root", {
				message: getAuthErrorMessage(
					e && typeof e === "object" && "code" in e
						? (e.code as string)
						: undefined,
				),
			});
		} finally {
			setIsLoadingOtp(false);
		}
	};

	const onSubmit = form.handleSubmit(async ({ code, trustDevice }) => {
		try {
			let error;

			if (verificationMethod === "totp") {
				const result = await authClient.twoFactor.verifyTotp({
					code,
					trustDevice,
				});
				error = result.error;
			} else if (verificationMethod === "otp") {
				const result = await authClient.twoFactor.verifyOtp({
					code,
					trustDevice,
				});
				error = result.error;
			} else if (verificationMethod === "backup") {
				const result = await authClient.twoFactor.verifyBackupCode({
					code,
					trustDevice,
				});
				error = result.error;
			}

			if (error) {
				throw error;
			}

			router.replace(redirectPath);
		} catch (e) {
			form.setError("root", {
				message: getAuthErrorMessage(
					e && typeof e === "object" && "code" in e
						? (e.code as string)
						: undefined,
				),
			});
		}
	});

	return (
		<>
			<h1 className="font-bold text-xl md:text-2xl">
				{t("auth.verify.title")}
			</h1>
			<p className="mt-1 mb-4 text-foreground/60">
				{verificationMethod === "totp"
					? t("auth.verify.message")
					: verificationMethod === "otp"
						? t("auth.verify.otpSent")
						: t("auth.verify.methods.backup")}
			</p>

			{/* Method selection buttons */}
			<div className="flex gap-2 mb-4">
				<Button
					variant={verificationMethod === "totp" ? "default" : "outline"}
					size="sm"
					onClick={() => setVerificationMethod("totp")}
				>
					{t("auth.verify.methods.totp")}
				</Button>
				<Button
					variant="outline"
					size="sm"
					onClick={sendOtp}
					loading={isLoadingOtp}
				>
					<MailIcon className="mr-1.5 size-4" />
					{t("auth.verify.sendOtp")}
				</Button>
				<Button
					variant={verificationMethod === "backup" ? "default" : "outline"}
					size="sm"
					onClick={() => setVerificationMethod("backup")}
				>
					{t("auth.verify.useBackupCode")}
				</Button>
			</div>

			<Form {...form}>
				<form
					className="flex flex-col items-stretch gap-4"
					onSubmit={onSubmit}
				>
					{form.formState.errors.root && (
						<Alert variant="error">
							<AlertTriangleIcon />
							<AlertTitle>
								{form.formState.errors.root.message}
							</AlertTitle>
						</Alert>
					)}

					<FormField
						control={form.control}
						name="code"
						render={({ field }) => (
							<FormItem>
								<FormLabel>{t("auth.verify.code")}</FormLabel>
								<FormControl>
									<InputOTP
										maxLength={6}
										{...field}
										autoComplete="one-time-code"
										onChange={(value) => {
											field.onChange(value);
											onSubmit();
										}}
									>
										<InputOTPGroup>
											<InputOTPSlot
												className="size-10 text-lg"
												index={0}
											/>
											<InputOTPSlot
												className="size-10 text-lg"
												index={1}
											/>
											<InputOTPSlot
												className="size-10 text-lg"
												index={2}
											/>
										</InputOTPGroup>
										<InputOTPSeparator className="opacity-40" />
										<InputOTPGroup>
											<InputOTPSlot
												className="size-10 text-lg"
												index={3}
											/>
											<InputOTPSlot
												className="size-10 text-lg"
												index={4}
											/>
											<InputOTPSlot
												className="size-10 text-lg"
												index={5}
											/>
										</InputOTPGroup>
									</InputOTP>
								</FormControl>
								<FormMessage />
							</FormItem>
						)}
					/>

					<FormField
						control={form.control}
						name="trustDevice"
						render={({ field }) => (
							<FormItem className="flex flex-row items-start space-x-3 space-y-0">
								<FormControl>
									<Checkbox
										checked={field.value}
										onCheckedChange={field.onChange}
									/>
								</FormControl>
								<div className="space-y-1 leading-none">
									<FormLabel className="text-sm font-normal">
										{t("auth.verify.trustDevice")}
									</FormLabel>
								</div>
							</FormItem>
						)}
					/>

					<Button loading={form.formState.isSubmitting}>
						{t("auth.verify.submit")}
					</Button>
				</form>
			</Form>

			<div className="mt-6 text-center text-sm">
				<Link href="/auth/login">
					<ArrowLeftIcon className="mr-1 inline size-4 align-middle" />
					{t("auth.verify.backToSignin")}
				</Link>
			</div>
		</>
	);
}
